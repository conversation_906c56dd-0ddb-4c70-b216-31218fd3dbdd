{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\order\\search.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\order\\search.vue", "mtime": 1754376311941}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _order = require(\"@/api/order\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: \"OrderSearch\",\n  data: function data() {\n    return {\n      loading: false,\n      searchFrom: {\n        orderNo: \"\",\n        productTitle: \"\",\n        type: \"2\",\n        dateLimit: \"\",\n        page: 1,\n        limit: 20,\n        total: 0\n      },\n      tableData: [],\n      statusList: [{\n        value: \"4\",\n        label: \"unknown\"\n      }, {\n        value: \"5\",\n        label: \"ordered\"\n      }, {\n        value: \"6\",\n        label: \"settled\"\n      }, {\n        value: \"7\",\n        label: \"refunded\"\n      }, {\n        value: \"8\",\n        label: \"frozen\"\n      }, {\n        value: \"9\",\n        label: \"deducted\"\n      }],\n      statusMap: {\n        4: \"unknown\",\n        5: \"ordered\",\n        6: \"settled\",\n        7: \"refunded\",\n        8: \"frozen\",\n        9: \"deducted\"\n      }\n    };\n  },\n  created: function created() {},\n  mounted: function mounted() {\n    this.getList();\n  },\n  methods: {\n    // 列表\n    getList: function getList(num) {\n      var _this2 = this;\n      var _this = this;\n      this.loading = true;\n      this.searchFrom.page = num ? num : this.searchFrom.page;\n      console.log('发送请求参数:', this.searchFrom);\n      (0, _order.orderListApi)(this.searchFrom).then(function (res) {\n        console.log('API响应数据:', res);\n        console.log('res.total:', res.total);\n        console.log('res.list长度:', res.list ? res.list.length : 0);\n        _this2.tableData = res.list || [];\n        _this2.tableData.forEach(function (item) {\n          item.payCount = item.productList ? item.productList.length : 0;\n          // item.statusLabel = this.statusList.filter(\n          //   i => i.value == item.status\n          // )[0].label;\n          item.productName = item.productList[0].productName;\n          item.actualCommission = item.productList[0].actualCommission;\n          item.commissionRate = item.productList[0].commissionRate;\n          item.contentId = item.productList[0].contentId;\n          item.estimatedCommission = item.productList[0].estimatedCommission;\n          item.price = _this.formatAmount(item.productList[0].price);\n          item.totalPrice = _this.formatAmount(item.totalPrice);\n          item.image = item.productList[0].image;\n          var _status = \"unknown\";\n          if (_this.statusMap[parseInt(item.status)]) {\n            _status = _this.statusMap[parseInt(item.status)];\n          }\n          item.sLabel = _this.$t(\"order.search.\" + _status);\n          console.log(_status, item.sLabel);\n        });\n        _this2.searchFrom.total = res.total;\n        console.log('设置total后:', _this2.searchFrom.total);\n        _this2.loading = false;\n      }).catch(function () {\n        _this2.loading = false;\n      });\n    },\n    formatAmount: function formatAmount(s) {\n      if (s == undefined) {\n        s = 0;\n      }\n      var s1 = (s / 1000).toFixed(3);\n      return s1;\n    },\n    //切换页数\n    pageChange: function pageChange(index) {\n      this.searchFrom.page = index;\n      this.getList();\n    },\n    //切换显示条数\n    sizeChange: function sizeChange(index) {\n      this.searchFrom.limit = index;\n      this.getList();\n    },\n    resetForm: function resetForm() {\n      this.searchFrom = {\n        orderNo: \"\",\n        productTitle: \"\",\n        type: \"2\",\n        dateLimit: \"\",\n        page: 1,\n        limit: 20,\n        total: 0\n      };\n      this.getList();\n    },\n    formatRate: function formatRate(s) {\n      if (s == undefined) {\n        s = 0;\n      }\n      return parseInt(s * 10000) / 100 + \"%\";\n    }\n  }\n};", null]}